import type { Context, Next, MiddlewareHandler } from "hono";
import { HTTPException } from "hono/http-exception";
import { authMiddleware, optionalAuthMiddleware } from "@/middlewares/auth";
import { apiCors, publicCors } from "./cors";

/**
 * Pattern matching for public endpoints
 */
export type RoutePattern = string | RegExp | ((path: string, method: string) => boolean);

/**
 * Configuration for public endpoints
 */
export interface PublicEndpointConfig {
  patterns: RoutePattern[];
  cors?: MiddlewareHandler;
  description?: string;
}

/**
 * Default public endpoint patterns
 */
const DEFAULT_PUBLIC_PATTERNS: RoutePattern[] = [
  // Health and info endpoints
  '/health',
  '/',
  '/doc',
  '/reference',
  
  // Authentication endpoints
  '/v1/auth/signup',
  '/v1/auth/signin',
  '/v1/auth/refresh',
  '/v1/auth/forgot-password',
  '/v1/auth/reset-password',
  '/v1/auth/callback',
  
  // Logs endpoints (using API key authentication)
  '/v1/logs',
  '/v1/logs/{id}',
  '/v1/logs/by-path',
  (path: string, method: string) => path === '/v1/logs' && method === 'DELETE',

  // OPTIONS requests (CORS preflight)
  (path: string, method: string) => method === 'OPTIONS',
];

/**
 * Configuration for different endpoint types
 */
export const ENDPOINT_CONFIGS = {
  public: {
    patterns: DEFAULT_PUBLIC_PATTERNS,
    cors: publicCors,
    description: 'Publicly accessible endpoints that do not require authentication',
  },
  authenticated: {
    patterns: [
      // Files endpoints
      '/v1/files/*',
      // Listings endpoints
      '/v1/listings/*',
      // Users endpoints
      '/v1/users/*',
    ] as RoutePattern[],
    cors: apiCors,
    description: 'Endpoints that require user authentication',
  },
  optional: {
    patterns: [] as RoutePattern[],
    cors: apiCors,
    description: 'Endpoints where authentication is optional',
  },
};

/**
 * Check if a route matches any of the given patterns
 */
function matchesPattern(path: string, method: string, patterns: RoutePattern[]): boolean {
  const result = patterns.some(pattern => {
    if (typeof pattern === 'string') {
      return path === pattern || path.startsWith(pattern + '/');
    }
    if (pattern instanceof RegExp) {
      return pattern.test(path);
    }
    if (typeof pattern === 'function') {
      return pattern(path, method);
    }
    return false;
  });
  
  // Debug logging for logs endpoints
  if (path.includes('/logs')) {
    console.log(`[DEBUG] Path: "${path}", Method: "${method}", Matches Public: ${result}`);
    console.log(`[DEBUG] Patterns:`, patterns);
  }
  
  return result;
}

/**
 * Determine the endpoint type based on the request path and method
 */
export function getEndpointType(path: string, method: string): keyof typeof ENDPOINT_CONFIGS {
  if (matchesPattern(path, method, ENDPOINT_CONFIGS.public.patterns)) {
    return 'public';
  }
  if (matchesPattern(path, method, ENDPOINT_CONFIGS.optional.patterns)) {
    return 'optional';
  }
  return 'authenticated';
}

/**
 * Smart authentication middleware that automatically determines
 * whether authentication is required based on the endpoint
 */
export function smartAuthMiddleware(): MiddlewareHandler {
  return async (c: Context, next: Next) => {
    const path = c.req.path;
    const method = c.req.method;
    const endpointType = getEndpointType(path, method);

    switch (endpointType) {
      case 'public':
        // No authentication required
        await next();
        break;
      case 'optional':
        // Optional authentication
        await optionalAuthMiddleware(c, next);
        break;
      case 'authenticated':
        // Required authentication
        await authMiddleware(c, next);
        break;
      default:
        await next();
    }
  };
}

/**
 * Create a middleware that handles both CORS and authentication
 * based on the endpoint configuration
 */
export function createSmartMiddleware(): MiddlewareHandler {
  return async (c: Context, next: Next) => {
    const path = c.req.path;
    const method = c.req.method;
    const endpointType = getEndpointType(path, method);
    const config = ENDPOINT_CONFIGS[endpointType];

    // Apply CORS first
    if (config.cors) {
      await config.cors(c, async () => {
        // Then apply authentication logic
        await smartAuthMiddleware()(c, next);
      });
    } else {
      await smartAuthMiddleware()(c, next);
    }
  };
}

/**
 * Add custom public endpoint patterns
 */
export function addPublicEndpoints(patterns: RoutePattern[]): void {
  ENDPOINT_CONFIGS.public.patterns.push(...patterns);
}

/**
 * Add custom optional auth endpoint patterns
 */
export function addOptionalAuthEndpoints(patterns: RoutePattern[]): void {
  ENDPOINT_CONFIGS.optional.patterns.push(...patterns);
}

/**
 * Utility to create a public endpoint middleware for specific routes
 */
export function publicEndpoint(): MiddlewareHandler {
  return async (c: Context, next: Next) => {
    // Apply public CORS
    await publicCors(c, next);
  };
}

/**
 * Utility to create an authenticated endpoint middleware
 */
export function authenticatedEndpoint(): MiddlewareHandler {
  return async (c: Context, next: Next) => {
    // Apply API CORS and then authentication
    await apiCors(c, async () => {
      await authMiddleware(c, next);
    });
  };
}

/**
 * Utility to create an optional auth endpoint middleware
 */
export function optionalAuthEndpoint(): MiddlewareHandler {
  return async (c: Context, next: Next) => {
    // Apply API CORS and then optional authentication
    await apiCors(c, async () => {
      await optionalAuthMiddleware(c, next);
    });
  };
}

/**
 * Convenience function to mark specific routes as public
 * Usage: app.use('/api/public/*', markAsPublic())
 */
export function markAsPublic(): MiddlewareHandler {
  return publicEndpoint();
}

/**
 * Convenience function to mark specific routes as requiring authentication
 * Usage: app.use('/api/protected/*', markAsAuthenticated())
 */
export function markAsAuthenticated(): MiddlewareHandler {
  return authenticatedEndpoint();
}

/**
 * Convenience function to mark specific routes as having optional authentication
 * Usage: app.use('/api/optional/*', markAsOptionalAuth())
 */
export function markAsOptionalAuth(): MiddlewareHandler {
  return optionalAuthEndpoint();
}

/**
 * Helper to check if current request is for a public endpoint
 */
export function isPublicEndpoint(c: Context): boolean {
  return getEndpointType(c.req.path, c.req.method) === 'public';
}

/**
 * Helper to check if current request requires authentication
 */
export function requiresAuthentication(c: Context): boolean {
  return getEndpointType(c.req.path, c.req.method) === 'authenticated';
}

/**
 * Configuration helper to setup the middleware system
 */
export interface MiddlewareSystemConfig {
  additionalPublicPatterns?: RoutePattern[];
  additionalOptionalPatterns?: RoutePattern[];
  corsConfig?: {
    origins?: string[];
    allowHeaders?: string[];
    allowMethods?: string[];
  };
}

/**
 * Initialize the middleware system with custom configuration
 */
export function setupMiddlewareSystem(config?: MiddlewareSystemConfig): void {
  if (config?.additionalPublicPatterns) {
    addPublicEndpoints(config.additionalPublicPatterns);
  }
  
  if (config?.additionalOptionalPatterns) {
    addOptionalAuthEndpoints(config.additionalOptionalPatterns);
  }
} 