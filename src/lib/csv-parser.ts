import <PERSON> from 'papa<PERSON>se';
import { HTTPException } from 'hono/http-exception';

export interface CsvRow {
  business_name: string;
  industry: string;
  asking_price?: string;
  cash_flow_sde?: string;
  annual_revenue?: string;
  status?: string;
  general_location?: string;
  year_established?: string;
  employees?: string;
  owner_hours_week?: string;
  date_listed?: string;
  days_listed?: string;
  business_description?: string;
  brief_description?: string;
  financial_details?: string;
  operations?: string;
  growth_opportunities?: string;
  reason_for_sale?: string;
  training_period?: string;
  support_type?: string;
  financing_available?: string;
  equipment_highlights?: string;
  supplier_relationships?: string;
  real_estate_status?: string;
  lease_details?: string;
}

export interface ParsedCsvData {
  data: CsvRow[];
  errors: Array<{
    row: number;
    field: string;
    message: string;
  }>;
}

export class CsvParser {
  private static readonly REQUIRED_FIELDS = ['business_name', 'industry'];
  private static readonly MAX_ROWS = 1000;

  /**
   * Parse CSV file content and validate required fields
   */
  static async parseCsvFile(file: File): Promise<ParsedCsvData> {
    // Validate file type
    if (!file.type.includes('csv') && !file.name.endsWith('.csv')) {
      throw new HTTPException(400, { message: 'File must be a CSV file' });
    }

    // Read file content
    const fileContent = await file.text();
    
    if (!fileContent.trim()) {
      throw new HTTPException(400, { message: 'CSV file is empty' });
    }

    // Parse CSV
    const parseResult = Papa.parse<CsvRow>(fileContent, {
      header: true,
      skipEmptyLines: true,
      transformHeader: (header: string) => header.trim().toLowerCase().replace(/\s+/g, '_'),
      transform: (value: string) => value.trim(),
    });

    if (parseResult.errors.length > 0) {
      const errorMessages = parseResult.errors.map(err => `Row ${err.row}: ${err.message}`);
      throw new HTTPException(400, { 
        message: `CSV parsing errors: ${errorMessages.join('; ')}` 
      });
    }

    const data = parseResult.data;

    if (data.length === 0) {
      throw new HTTPException(400, { message: 'CSV file contains no data rows' });
    }

    if (data.length > this.MAX_ROWS) {
      throw new HTTPException(400, { 
        message: `CSV file contains too many rows. Maximum allowed: ${this.MAX_ROWS}` 
      });
    }

    // Validate required fields and collect errors
    const errors: Array<{ row: number; field: string; message: string }> = [];
    
    data.forEach((row, index) => {
      this.REQUIRED_FIELDS.forEach(field => {
        if (!row[field as keyof CsvRow] || row[field as keyof CsvRow]?.trim() === '') {
          errors.push({
            row: index + 2, // +2 because index is 0-based and we skip header
            field,
            message: `Required field '${field}' is missing or empty`
          });
        }
      });
    });

    return {
      data,
      errors
    };
  }

  /**
   * Convert CSV row to listing data format
   */
  static csvRowToListingData(row: CsvRow, workspaceId: string, createdBy: string): any {
    // Helper function to safely parse JSON strings
    const safeJsonParse = (jsonString?: string, defaultValue: any = {}) => {
      if (!jsonString || jsonString.trim() === '') return defaultValue;
      try {
        return JSON.parse(jsonString);
      } catch {
        return defaultValue;
      }
    };

    // Helper function to parse comma-separated values
    const parseCommaSeparated = (value?: string | string[]): string[] => {
      if (value === undefined || value === null || value === '') return [];
      if (Array.isArray(value)) return value;
      const stringValue = typeof value === 'string' ? value.trim() : String(value);
      if (stringValue === '') return [];
      return stringValue.split(',').map(item => item.trim()).filter(item => item.length > 0);
    };

    // Helper function to safely parse numbers
    const safeParseNumber = (value?: string | number): number | undefined => {
      if (value === undefined || value === null || value === '') return undefined;
      const stringValue = typeof value === 'string' ? value.trim() : String(value);
      if (stringValue === '') return undefined;
      const parsed = parseFloat(stringValue.replace(/[,$]/g, ''));
      return isNaN(parsed) ? undefined : parsed;
    };

    // Helper function to safely parse integers
    const safeParseInt = (value?: string | number): number | undefined => {
      if (value === undefined || value === null || value === '') return undefined;
      const stringValue = typeof value === 'string' ? value.trim() : String(value);
      if (stringValue === '') return undefined;
      const parsed = parseInt(stringValue.replace(/[,$]/g, ''), 10);
      return isNaN(parsed) ? undefined : parsed;
    };

    // Helper function to safely parse boolean
    const safeParseBoolean = (value?: string | boolean): boolean | undefined => {
      if (value === undefined || value === null || value === '') return undefined;
      if (typeof value === 'boolean') return value;
      const stringValue = typeof value === 'string' ? value.trim() : String(value);
      if (stringValue === '') return undefined;
      const lower = stringValue.toLowerCase();
      if (lower === 'true' || lower === 'yes' || lower === '1') return true;
      if (lower === 'false' || lower === 'no' || lower === '0') return false;
      return undefined;
    };

    // Parse financial details
    const financialDetails = safeJsonParse(row.financial_details, {});
    
    // Parse operations
    const operations = safeJsonParse(row.operations, {});
    
    // Parse lease details
    const leaseDetails = safeJsonParse(row.lease_details, {});

    // Calculate days_listed if date_listed is provided
    let daysListed: number | undefined;
    if (row.date_listed) {
      try {
        const listingDate = new Date(row.date_listed);
        const now = new Date();
        daysListed = Math.floor((now.getTime() - listingDate.getTime()) / (1000 * 60 * 60 * 24));
      } catch {
        // If date parsing fails, use the provided days_listed value
        daysListed = safeParseInt(row.days_listed);
      }
    } else {
      daysListed = safeParseInt(row.days_listed);
    }

    return {
      // Required fields
      business_name: row.business_name,
      industry: row.industry,
      
      // Core business fields
      asking_price: safeParseNumber(row.asking_price),
      cash_flow_sde: safeParseNumber(row.cash_flow_sde),
      annual_revenue: safeParseNumber(row.annual_revenue),
      status: row.status || 'draft',
      general_location: row.general_location,
      year_established: safeParseInt(row.year_established),
      employees: safeParseInt(row.employees),
      owner_hours_week: safeParseInt(row.owner_hours_week),
      date_listed: row.date_listed,
      
      // Metadata
      workspace_id: workspaceId,
      created_by: createdBy,
      
      // Details object
      details: {
        business_description: row.business_description,
        brief_description: row.brief_description,
        financial_details: {
          revenue_2023: safeParseNumber(financialDetails.revenue_2023 || financialDetails['2023_revenue']),
          ebitda: safeParseNumber(financialDetails.ebitda),
          assets_included: Array.isArray(financialDetails.assets_included) 
            ? financialDetails.assets_included 
            : parseCommaSeparated(financialDetails.assets_included),
          inventory_value: safeParseNumber(financialDetails.inventory_value),
          additional_financial_info: financialDetails.additional_financial_info || {}
        },
        operations: {
          business_model: operations.business_model || '',
          key_features: Array.isArray(operations.key_features) 
            ? operations.key_features 
            : parseCommaSeparated(operations.key_features),
          competitive_advantages: Array.isArray(operations.competitive_advantages) 
            ? operations.competitive_advantages 
            : parseCommaSeparated(operations.competitive_advantages),
          operational_details: operations.operational_details || {}
        },
        growth_opportunities: parseCommaSeparated(row.growth_opportunities),
        reason_for_sale: row.reason_for_sale,
        training_period: row.training_period,
        support_type: row.support_type,
        financing_available: safeParseBoolean(row.financing_available) || false,
        equipment_highlights: parseCommaSeparated(row.equipment_highlights),
        supplier_relationships: row.supplier_relationships,
        real_estate_status: row.real_estate_status,
        lease_details: {
          lease_terms: leaseDetails.lease_terms || '',
          monthly_rent: safeParseNumber(leaseDetails.monthly_rent),
          lease_expiration: leaseDetails.lease_expiration,
          renewal_options: leaseDetails.renewal_options || '',
          landlord_info: leaseDetails.landlord_info || {}
        }
      }
    };
  }
}
