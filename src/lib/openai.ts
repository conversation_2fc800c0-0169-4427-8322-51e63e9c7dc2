import OpenAI from 'openai';
import env from '@/env';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: env.OPENAI_API_KEY,
});

export interface ChatCompletionRequest {
  systemPrompt: string;
  userInput: string;
  model?: string;
  maxTokens?: number;
  temperature?: number;
}

export interface ChatCompletionResponse {
  content: string;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  model: string;
  finish_reason: string | null;
}

export class OpenAIService {
  /**
   * Call OpenAI chat completion with a system prompt and user input
   */
  static async chatCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse> {
    const {
      systemPrompt,
      userInput,
      // model = 'gpt-3.5-turbo',
      // maxTokens = 1000,
      // temperature = 0.7
    } = request;

    try {
      const completion = await openai.chat.completions.create({
        model: 'gpt-4.1',
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: userInput
          }
        ],
        // max_tokens: maxTokens,
        // temperature,
      });

      const choice = completion.choices[0];
      if (!choice?.message?.content) {
        throw new Error('No content returned from OpenAI');
      }
      // return choice.message.content;
      return {
        content: choice.message.content,
        usage: completion.usage ? {
          prompt_tokens: completion.usage.prompt_tokens,
          completion_tokens: completion.usage.completion_tokens,
          total_tokens: completion.usage.total_tokens,
        } : undefined,
        model: completion.model,
        finish_reason: choice.finish_reason,
      };
    } catch (error) {
      if (error instanceof OpenAI.APIError) {
        throw new Error(`OpenAI API Error: ${error.message}`);
      }
      throw error;
    }
  }

}

export default OpenAIService;
