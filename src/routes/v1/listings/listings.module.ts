import { createRouter } from "@/lib/create-app";
import { authenticatedEndpoint } from "@/lib/middleware-utils";
import * as routes from "./listings.routes";
import * as handlers from "./listings.controller";

const router = createRouter()
  .openapi(routes.listListingsRoute, handlers.listListings)
  .openapi(routes.getListingRoute, handlers.getListing)
  .openapi(routes.createListingRoute, handlers.createListing)
  .openapi(routes.updateListingRoute, handlers.updateListing)
  .openapi(routes.deleteListingRoute, handlers.deleteListing)
  .openapi(routes.bulkCreateListingsRoute, handlers.bulkCreateListings)
  .openapi(routes.bulkCreateListingsCsvRoute, handlers.bulkCreateListingsCsv)
  .openapi(routes.updateListingStatusRoute, handlers.updateListingStatus)
  .openapi(routes.getListingStatusHistoryRoute, handlers.getListingStatusHistory)
  // AI-powered routes
  .openapi(routes.generateListingDescriptionRoute, handlers.generateListingDescription)
  .openapi(routes.analyzeListingDataRoute, handlers.analyzeListingData)
  .openapi(routes.generateListingInsightsRoute, handlers.generateListingInsights)
  .openapi(routes.customAICompletionRoute, handlers.customAICompletion);

// No middleware applied - rely on global smart middleware pattern matching
export default router;