import { createRoute, z } from "@hono/zod-openapi";

// Enums for validation
const ListingStatusEnum = z.enum([
  'draft', 'active', 'under_contract', 'sold', 'confidential', 'expired', 'withdrawn'
]).openapi("ListingStatus");

const RealEstateStatusEnum = z.enum([
  'owned', 'leased', 'included', 'not_included', 'negotiable'
]).openapi("RealEstateStatus");

// Request schemas
const createListingRequestSchema = z.object({
  // Core business fields
  business_name: z.string().min(1, "Business name is required"),
  industry: z.string().min(1, "Industry is required"),
  asking_price: z.number().positive().optional(),
  cash_flow_sde: z.number().optional(),
  annual_revenue: z.number().positive().optional(),
  status: ListingStatusEnum.optional().default('draft'),
  general_location: z.string().optional(),
  year_established: z.number().int().min(1800).max(new Date().getFullYear()).optional(),
  employees: z.number().int().min(0).optional(),
  owner_hours_week: z.number().int().min(0).max(168).optional(),
  date_listed: z.string().date().optional(),
  
  // Assignment
  assigned_to: z.string().uuid().optional(),
  
  // Details object
  details: z.object({
    business_description: z.string().optional(),
    brief_description: z.string().optional(),
    financial_details: z.object({
      revenue_2023: z.number().optional(),
      ebitda: z.number().optional(),
      assets_included: z.array(z.string()).optional(),
      inventory_value: z.number().optional(),
      additional_financial_info: z.record(z.any()).optional(),
    }).optional(),
    operations: z.object({
      business_model: z.string().optional(),
      key_features: z.array(z.string()).optional(),
      competitive_advantages: z.array(z.string()).optional(),
      operational_details: z.record(z.any()).optional(),
    }).optional(),
    growth_opportunities: z.array(z.string()).optional(),
    reason_for_sale: z.string().optional(),
    training_period: z.string().optional(),
    support_type: z.string().optional(),
    financing_available: z.boolean().optional(),
    equipment_highlights: z.array(z.string()).optional(),
    supplier_relationships: z.string().optional(),
    real_estate_status: RealEstateStatusEnum.optional(),
    lease_details: z.object({
      lease_terms: z.string().optional(),
      monthly_rent: z.number().optional(),
      lease_expiration: z.string().date().optional(),
      renewal_options: z.string().optional(),
      landlord_info: z.record(z.any()).optional(),
    }).optional(),
  }).optional(),
  
  // Legacy fields for compatibility
  title: z.string().optional(),
  description: z.string().optional(),
  team_visibility: z.enum(['all', 'assigned_only', 'admins_only']).optional().default('all'),
}).openapi("CreateListingRequest");

const updateListingRequestSchema = createListingRequestSchema.partial().openapi("UpdateListingRequest");

const bulkCreateListingRequestSchema = z.object({
  listings: z.array(createListingRequestSchema).min(1).max(100),
}).openapi("BulkCreateListingRequest");

const updateListingStatusRequestSchema = z.object({
  status: ListingStatusEnum,
  reason: z.string().optional(),
  notes: z.string().optional(),
}).openapi("UpdateListingStatusRequest");

// Response schemas
const listingDetailsSchema = z.object({
  id: z.string().uuid(),
  listing_id: z.string().uuid(),
  business_description: z.string().optional(),
  brief_description: z.string().optional(),
  financial_details: z.record(z.any()),
  operations: z.record(z.any()),
  growth_opportunities: z.array(z.string()).optional(),
  reason_for_sale: z.string().optional(),
  training_period: z.string().optional(),
  support_type: z.string().optional(),
  financing_available: z.boolean(),
  equipment_highlights: z.array(z.string()).optional(),
  supplier_relationships: z.string().optional(),
  real_estate_status: z.string().optional(),
  lease_details: z.record(z.any()),
  created_at: z.string(),
  updated_at: z.string(),
}).openapi("ListingDetails");

const listingSchema = z.object({
  id: z.string().uuid(),
  workspace_id: z.string().uuid(),
  created_by: z.string().uuid(),
  assigned_to: z.string().uuid().optional(),
  // Core business fields
  business_name: z.string(),
  industry: z.string(),
  asking_price: z.number().optional(),
  cash_flow_sde: z.number().optional(),
  annual_revenue: z.number().optional(),
  status: z.string(),
  general_location: z.string().optional(),
  year_established: z.number().optional(),
  employees: z.number().optional(),
  owner_hours_week: z.number().optional(),
  date_listed: z.string().optional(),
  days_listed: z.number().int().optional(),
  // Legacy fields
  title: z.string().optional(),
  description: z.string().optional(),
  price: z.number().optional(),
  team_visibility: z.string(),
  // Metadata
  created_at: z.string(),
  updated_at: z.string(),
  // Relations
  details: listingDetailsSchema.optional(),
}).openapi("Listing");

const listingListResponseSchema = z.object({
  success: z.boolean(),
  data: z.array(listingSchema),
  pagination: z.object({
    page: z.number(),
    limit: z.number(),
    total: z.number(),
    pages: z.number(),
  }),
}).openapi("ListingListResponse");

const listingResponseSchema = z.object({
  success: z.boolean(),
  data: listingSchema,
}).openapi("ListingResponse");

const bulkCreateResponseSchema = z.object({
  success: z.boolean(),
  data: z.object({
    created: z.array(listingSchema),
    failed: z.array(z.object({
      index: z.number(),
      error: z.string(),
      data: z.record(z.any()),
    })),
  }),
}).openapi("BulkCreateResponse");

const statusChangeResponseSchema = z.object({
  success: z.boolean(),
  data: z.object({
    listing: listingSchema,
    status_change: z.object({
      id: z.string().uuid(),
      from_status: z.string().optional(),
      to_status: z.string(),
      reason: z.string().optional(),
      notes: z.string().optional(),
      changed_by: z.string().uuid(),
      created_at: z.string(),
    }),
  }),
}).openapi("StatusChangeResponse");

// Routes
export const listListingsRoute = createRoute({
  method: "get",
  path: "/v1/listings",
  request: {
    query: z.object({
      page: z.string().regex(/^\d+$/).transform(Number).optional().default("1"),
      limit: z.string().regex(/^\d+$/).transform(Number).optional().default("20"),
      status: z.string().optional(),
      industry: z.string().optional(),
      assigned_to: z.string().uuid().optional(),
      min_price: z.string().regex(/^\d+(\.\d+)?$/).transform(Number).optional(),
      max_price: z.string().regex(/^\d+(\.\d+)?$/).transform(Number).optional(),
      location: z.string().optional(),
      sort_by: z.enum(['created_at', 'updated_at', 'asking_price', 'business_name', 'date_listed', 'days_listed']).optional().default('created_at'),
      sort_order: z.enum(['asc', 'desc']).optional().default('desc'),
      search: z.string().optional(),
    }),
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: listingListResponseSchema,
        },
      },
      description: "List of listings retrieved successfully",
    },
    401: {
      description: "Unauthorized - authentication required",
    },
  },
  tags: ["Listings"],
});

export const getListingRoute = createRoute({
  method: "get",
  path: "/v1/listings/{listing_id}",
  request: {
    params: z.object({
      listing_id: z.string().uuid(),
    }),
    query: z.object({
      include_details: z.enum(['true', 'false']).optional().default('true'),
    }),
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: listingResponseSchema,
        },
      },
      description: "Listing retrieved successfully",
    },
    404: {
      description: "Listing not found",
    },
    401: {
      description: "Unauthorized - authentication required",
    },
    403: {
      description: "Forbidden - insufficient permissions",
    },
  },
  tags: ["Listings"],
});

export const createListingRoute = createRoute({
  method: "post",
  path: "/v1/listings",
  request: {
    body: {
      content: {
        "application/json": {
          schema: createListingRequestSchema,
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        "application/json": {
          schema: listingResponseSchema,
        },
      },
      description: "Listing created successfully",
    },
    400: {
      description: "Bad request - validation errors",
    },
    401: {
      description: "Unauthorized - authentication required",
    },
  },
  tags: ["Listings"],
});

export const updateListingRoute = createRoute({
  method: "put",
  path: "/v1/listings/{listing_id}",
  request: {
    params: z.object({
      listing_id: z.string().uuid(),
    }),
    body: {
      content: {
        "application/json": {
          schema: updateListingRequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: listingResponseSchema,
        },
      },
      description: "Listing updated successfully",
    },
    404: {
      description: "Listing not found",
    },
    400: {
      description: "Bad request - validation errors",
    },
    401: {
      description: "Unauthorized - authentication required",
    },
    403: {
      description: "Forbidden - insufficient permissions",
    },
  },
  tags: ["Listings"],
});

export const deleteListingRoute = createRoute({
  method: "delete",
  path: "/v1/listings/{listing_id}",
  request: {
    params: z.object({
      listing_id: z.string().uuid(),
    }),
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            success: z.boolean(),
            message: z.string(),
          }),
        },
      },
      description: "Listing deleted successfully",
    },
    404: {
      description: "Listing not found",
    },
    401: {
      description: "Unauthorized - authentication required",
    },
    403: {
      description: "Forbidden - insufficient permissions",
    },
  },
  tags: ["Listings"],
});

export const bulkCreateListingsRoute = createRoute({
  method: "post",
  path: "/v1/listings/bulk",
  request: {
    body: {
      content: {
        "application/json": {
          schema: bulkCreateListingRequestSchema,
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        "application/json": {
          schema: bulkCreateResponseSchema,
        },
      },
      description: "Bulk listing creation completed",
    },
    400: {
      description: "Bad request - validation errors",
    },
    401: {
      description: "Unauthorized - authentication required",
    },
  },
  tags: ["Listings"],
});

// CSV bulk import schema
const csvBulkImportRequestSchema = z.object({
  business_name: z.string().min(1, "Business name is required").describe("**Required** - Name of the business"),
  industry: z.string().min(1, "Industry is required").describe("**Required** - Industry category"),
  asking_price: z.string().optional().describe("Asking price for the business"),
  cash_flow_sde: z.string().optional().describe("Seller's Discretionary Earnings (SDE)"),
  annual_revenue: z.string().optional().describe("Annual revenue of the business"),
  status: z.string().optional().describe("Listing status (Active, Under Contract, Sold, Confidential, etc.)"),
  general_location: z.string().optional().describe("General location (city/state/region)"),
  year_established: z.string().optional().describe("Year the business was established"),
  employees: z.string().optional().describe("Number of employees"),
  owner_hours_week: z.string().optional().describe("Owner hours per week"),
  date_listed: z.string().optional().describe("Date when listing was created (YYYY-MM-DD format)"),
  days_listed: z.string().optional().describe("Number of days the listing has been active"),
  business_description: z.string().optional().describe("Detailed business description"),
  brief_description: z.string().optional().describe("Brief business summary"),
  financial_details: z.string().optional().describe("JSON string containing 2023_revenue, ebitda, assets_included, inventory_value"),
  operations: z.string().optional().describe("JSON string containing business_model, key_features, competitive_advantages"),
  growth_opportunities: z.string().optional().describe("Comma-separated list of growth opportunities"),
  reason_for_sale: z.string().optional().describe("Reason for selling the business"),
  training_period: z.string().optional().describe("Training period offered to buyer"),
  support_type: z.string().optional().describe("Type of support provided to buyer"),
  financing_available: z.string().optional().describe("Whether financing is available (true/false)"),
  equipment_highlights: z.string().optional().describe("Comma-separated list of equipment highlights"),
  supplier_relationships: z.string().optional().describe("Information about supplier relationships"),
  real_estate_status: z.string().optional().describe("Real estate status (owned, leased, included, not_included, negotiable)"),
  lease_details: z.string().optional().describe("JSON string containing lease_terms, monthly_rent, lease_expiration, renewal_options"),
}).openapi("CsvBulkImportRequest");

export const bulkCreateListingsCsvRoute = createRoute({
  method: "post",
  path: "/v1/listings/bulk/csv",
  request: {
    body: {
      content: {
        "multipart/form-data": {
          schema: z.object({
            file: z.any().describe("CSV file containing listing data"),
          }),
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        "application/json": {
          schema: bulkCreateResponseSchema,
        },
      },
      description: "CSV bulk import completed successfully",
    },
    400: {
      description: "Bad request - validation errors or invalid CSV format",
    },
    401: {
      description: "Unauthorized - authentication required",
    },
    413: {
      description: "Payload too large - file size exceeds limit",
    },
  },
  tags: ["Listings"],
});

export const updateListingStatusRoute = createRoute({
  method: "patch",
  path: "/v1/listings/{listing_id}/status",
  request: {
    params: z.object({
      listing_id: z.string().uuid(),
    }),
    body: {
      content: {
        "application/json": {
          schema: updateListingStatusRequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: statusChangeResponseSchema,
        },
      },
      description: "Listing status updated successfully",
    },
    404: {
      description: "Listing not found",
    },
    400: {
      description: "Bad request - validation errors",
    },
    401: {
      description: "Unauthorized - authentication required",
    },
    403: {
      description: "Forbidden - insufficient permissions",
    },
  },
  tags: ["Listings"],
});

export const getListingStatusHistoryRoute = createRoute({
  method: "get",
  path: "/v1/listings/{listing_id}/status-history",
  request: {
    params: z.object({
      listing_id: z.string().uuid(),
    }),
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            success: z.boolean(),
            data: z.array(z.object({
              id: z.string().uuid(),
              from_status: z.string().optional(),
              to_status: z.string(),
              reason: z.string().optional(),
              notes: z.string().optional(),
              changed_by: z.string().uuid(),
              changed_by_name: z.string().optional(),
              created_at: z.string(),
            })),
          }),
        },
      },
      description: "Listing status history retrieved successfully",
    },
    404: {
      description: "Listing not found",
    },
    401: {
      description: "Unauthorized - authentication required",
    },
    403: {
      description: "Forbidden - insufficient permissions",
    },
  },
  tags: ["Listings"],
});

// AI-powered routes
export const generateListingDescriptionRoute = createRoute({
  method: "post",
  path: "/v1/listings/{listing_id}/ai/generate-description",
  request: {
    params: z.object({
      listing_id: z.string().uuid(),
    }),
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            success: z.boolean(),
            data: z.object({
              description: z.string(),
            }),
          }),
        },
      },
      description: "AI-generated listing description created successfully",
    },
    404: {
      description: "Listing not found",
    },
    401: {
      description: "Unauthorized - authentication required",
    },
    403: {
      description: "Forbidden - insufficient permissions",
    },
    500: {
      description: "Internal server error - AI service unavailable",
    },
  },
  tags: ["Listings", "AI"],
});

export const analyzeListingDataRoute = createRoute({
  method: "post",
  path: "/v1/listings/{listing_id}/ai/analyze",
  request: {
    params: z.object({
      listing_id: z.string().uuid(),
    }),
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            success: z.boolean(),
            data: z.object({
              analysis: z.string(),
            }),
          }),
        },
      },
      description: "AI analysis of listing data completed successfully",
    },
    404: {
      description: "Listing not found",
    },
    401: {
      description: "Unauthorized - authentication required",
    },
    403: {
      description: "Forbidden - insufficient permissions",
    },
    500: {
      description: "Internal server error - AI service unavailable",
    },
  },
  tags: ["Listings", "AI"],
});

export const generateListingInsightsRoute = createRoute({
  method: "post",
  path: "/v1/listings/{listing_id}/ai/insights",
  request: {
    params: z.object({
      listing_id: z.string().uuid(),
    }),
    body: {
      content: {
        "application/json": {
          schema: z.object({
            custom_prompt: z.string().optional().describe("Custom system prompt for AI analysis"),
          }),
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            success: z.boolean(),
            data: z.object({
              insights: z.string(),
            }),
          }),
        },
      },
      description: "AI insights generated successfully",
    },
    404: {
      description: "Listing not found",
    },
    401: {
      description: "Unauthorized - authentication required",
    },
    403: {
      description: "Forbidden - insufficient permissions",
    },
    500: {
      description: "Internal server error - AI service unavailable",
    },
  },
  tags: ["Listings", "AI"],
});

export const customAICompletionRoute = createRoute({
  method: "post",
  path: "/v1/listings/ai/chat",
  request: {
    body: {
      content: {
        "application/json": {
          schema: z.object({
            system_prompt: z.string().min(1, "System prompt is required"),
            user_input: z.string().min(1, "User input is required"),
            model: z.string().optional().default("gpt-3.5-turbo"),
            max_tokens: z.number().int().min(1).max(4000).optional().default(1000),
            temperature: z.number().min(0).max(2).optional().default(0.7),
          }),
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            success: z.boolean(),
            data: z.object({
              content: z.string(),
              model: z.string(),
              usage: z.object({
                prompt_tokens: z.number(),
                completion_tokens: z.number(),
                total_tokens: z.number(),
              }).optional(),
            }),
          }),
        },
      },
      description: "AI chat completion successful",
    },
    400: {
      description: "Bad request - validation errors",
    },
    401: {
      description: "Unauthorized - authentication required",
    },
    500: {
      description: "Internal server error - AI service unavailable",
    },
  },
  tags: ["Listings", "AI"],
});