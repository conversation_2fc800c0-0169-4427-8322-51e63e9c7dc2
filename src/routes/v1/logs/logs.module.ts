import { createRouter } from "@/lib/create-app";
import * as routes from "./logs.routes";
import * as handlers from "./logs.controller";

const router = createRouter()
  .openapi(routes.getLogsRoute, handlers.getLogs)
  .openapi(routes.getLogByIdRoute, handlers.getLogById)
  .openapi(routes.getLogsByPathRoute, handlers.getLogsByPath)
  .openapi(routes.deleteLogsRoute, handlers.deleteLogs);

// No middleware applied - rely on global smart middleware pattern matching
export default router;