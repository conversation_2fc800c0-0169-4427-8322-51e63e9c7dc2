import { HTTPException } from "hono/http-exception";
import { eq, and, desc, count, lt, gte, lte, like } from "drizzle-orm";
import db from "@/db";
import { apiLogs } from "@/db/schema";

export interface LogFilters {
  page?: number;
  limit?: number;
  method?: string;
  status_code?: number;
  path?: string;
  user_id?: string;
  from_date?: string;
  to_date?: string;
}

export class LogsService {
  /**
   * Get API logs with filtering and pagination
   */
  static async getLogs(filters: LogFilters, workspaceId?: string | undefined) {
    const { 
      page = 1, 
      limit = 20, 
      method, 
      status_code, 
      path, 
      user_id, 
      from_date, 
      to_date 
    } = filters;

    // Build where conditions
    const conditions = [];

    // Filter by workspace if provided (for multi-tenant access)
    if (workspaceId) {
      conditions.push(eq(apiLogs.workspaceId, workspaceId));
    }

    if (method) {
      conditions.push(eq(apiLogs.method, method.toUpperCase()));
    }

    if (status_code) {
      conditions.push(eq(apiLogs.statusCode, status_code));
    }

    if (path) {
      conditions.push(like(apiLogs.path, `%${path}%`));
    }

    if (user_id) {
      conditions.push(eq(apiLogs.userId, user_id));
    }

    if (from_date) {
      conditions.push(gte(apiLogs.createdAt, from_date));
    }

    if (to_date) {
      conditions.push(lte(apiLogs.createdAt, to_date));
    }

    // Get total count for pagination
    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;
    const [{ total }] = await db
      .select({ total: count() })
      .from(apiLogs)
      .where(whereClause);

    // Apply pagination and get results
    const offset = (page - 1) * limit;
    const results = await db
      .select({
        id: apiLogs.id,
        method: apiLogs.method,
        url: apiLogs.url,
        path: apiLogs.path,
        statusCode: apiLogs.statusCode,
        duration: apiLogs.duration,
        userAgent: apiLogs.userAgent,
        ipAddress: apiLogs.ipAddress,
        userId: apiLogs.userId,
        workspaceId: apiLogs.workspaceId,
        errorMessage: apiLogs.errorMessage,
        createdAt: apiLogs.createdAt,
      })
      .from(apiLogs)
      .where(whereClause)
      .orderBy(desc(apiLogs.createdAt))
      .limit(limit)
      .offset(offset);

    return {
      data: results,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * Get a single log entry by ID
   */
  static async getLogById(id: string, workspaceId?: string | undefined) {
    const conditions = [eq(apiLogs.id, id)];
    
    // Filter by workspace if provided (for multi-tenant access)
    if (workspaceId) {
      conditions.push(eq(apiLogs.workspaceId, workspaceId));
    }

    const log = await db
      .select()
      .from(apiLogs)
      .where(and(...conditions))
      .limit(1);

    if (log.length === 0) {
      throw new HTTPException(404, { message: "Log entry not found" });
    }

    return log[0];
  }

  /**
   * Delete logs older than specified date
   */
  static async deleteOldLogs(olderThan: string) {
    const result = await db
      .delete(apiLogs)
      .where(lt(apiLogs.createdAt, olderThan))
      .returning({ id: apiLogs.id });

    const deletedCount = result.length;

    return {
      deleted: deletedCount,
      message: `Deleted ${deletedCount} log entries`,
    };
  }

  /**
   * Get logs by specific path with optimized performance
   */
  static async getLogsByPath(
    path: string, 
    options: {
      page?: number;
      limit?: number;
      workspaceId?: string;
      method?: string;
      status_code?: number;
      from_date?: string;
      to_date?: string;
    } = {}
  ) {
    const { 
      page = 1, 
      limit = 20, 
      workspaceId, 
      method, 
      status_code, 
      from_date, 
      to_date 
    } = options;

    // Build optimized where conditions - path first for index usage
    const conditions = [eq(apiLogs.path, path)];

    // Add workspace filter early for multi-tenant optimization
    if (workspaceId) {
      conditions.push(eq(apiLogs.workspaceId, workspaceId));
    }

    // Add additional filters
    if (method) {
      conditions.push(eq(apiLogs.method, method.toUpperCase()));
    }

    if (status_code) {
      conditions.push(eq(apiLogs.statusCode, status_code));
    }

    if (from_date) {
      conditions.push(gte(apiLogs.createdAt, from_date));
    }

    if (to_date) {
      conditions.push(lte(apiLogs.createdAt, to_date));
    }

    const whereClause = and(...conditions);

    // Get total count for pagination (optimized with same where clause)
    const [{ total }] = await db
      .select({ total: count() })
      .from(apiLogs)
      .where(whereClause);

    // Apply pagination and get results with optimized ordering
    const offset = (page - 1) * limit;
    const results = await db
      .select({
        id: apiLogs.id,
        method: apiLogs.method,
        url: apiLogs.url,
        path: apiLogs.path,
        statusCode: apiLogs.statusCode,
        duration: apiLogs.duration,
        userAgent: apiLogs.userAgent,
        ipAddress: apiLogs.ipAddress,
        userId: apiLogs.userId,
        workspaceId: apiLogs.workspaceId,
        errorMessage: apiLogs.errorMessage,
        createdAt: apiLogs.createdAt,
      })
      .from(apiLogs)
      .where(whereClause)
      .orderBy(desc(apiLogs.createdAt)) // Use indexed column for ordering
      .limit(limit)
      .offset(offset);

    return {
      data: results,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
      summary: {
        path,
        totalRequests: total,
        ...(method && { method }),
        ...(status_code && { statusCode: status_code }),
      },
    };
  }

  /**
   * Get logs statistics
   */
  static async getLogsStats(workspaceId?: string) {
    const conditions = workspaceId ? [eq(apiLogs.workspaceId, workspaceId)] : [];
    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    const stats = await db
      .select({
        total: count(),
        errors: count(apiLogs.statusCode),
        avgDuration: count(apiLogs.duration),
      })
      .from(apiLogs)
      .where(whereClause);

    return stats[0];
  }
} 